name: Release Launcher

on:
  workflow_dispatch:

env:
  python_version: '3.12.8'
  pyinstaller_version: "6.11.1"

jobs:
  build-mac:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: true
      matrix:
        os: [ 'macos-13', 'macos-14' ]
        include:
          - os: 'macos-13'
            artifact-name: 'launcher-macos-amd64'
            move-command: 'mv dist/__main__ dist/launch'
            zip-command: 'zip -j dist/ZenithProxy-launcher-macos-amd64.zip dist/launch'
            sha1-command: 'shasum dist/launch | cut -d" " -f 1'
          - os: 'macos-14'
            artifact-name: 'launcher-macos-aarch64'
            move-command: 'mv dist/__main__ dist/launch'
            zip-command: 'zip -j dist/ZenithProxy-launcher-macos-aarch64.zip dist/launch'
            sha1-command: 'shasum dist/launch | cut -d" " -f 1'
    steps:
      - name: Check out repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - uses: actions/setup-python@v5
        with:
          python-version: ${{ env.python_version }}

      - name: Install PyInstaller
        run: |
          pip install -U pyinstaller==${{ env.pyinstaller_version }}
          pip install -r ./src/launcher/requirements.txt

      - name: Build launcher
        run: >
          pyinstaller --specpath ./build 
          -p ./src/launcher --onefile --noconfirm
          -i ../src/launcher/icon.ico
          ./src/launcher/__main__.py

      - name: Rename executable
        run: |
          ${{ matrix.move-command }}

      - name: Create zip
        run: |
          ${{ matrix.zip-command }}

      - name: Upload Binary
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.artifact-name }}
          path: dist/ZenithProxy-${{ matrix.artifact-name }}.zip

      - name: Get Binary Hash
        run: ${{ matrix.sha1-command }} > ZenithProxy-${{ matrix.artifact-name }}.sha1

      - name: Upload Launcher Hash Version
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.artifact-name }}.sha1
          path: ZenithProxy-${{ matrix.artifact-name }}.sha1

  # todo: probably possible to unify this with the mac matrix somehow
  build-ubuntu20-x64:
    runs-on: ${{ matrix.os }}
    container: # todo: this is the only diff with mac matrix
      image: catthehacker/ubuntu:act-20.04
    strategy:
      fail-fast: true
      matrix:
        os: [ 'ubuntu-latest']
        include:
          - os: 'ubuntu-latest'
            artifact-name: 'launcher-linux-amd64'
            move-command: 'mv dist/__main__ dist/launch'
            zip-command: 'zip -j dist/ZenithProxy-launcher-linux-amd64.zip dist/launch'
            sha1-command: 'sha1sum -b dist/launch | cut -d" " -f 1'
    steps:
      - name: Check out repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - uses: actions/setup-python@v5
        with:
          python-version: ${{ env.python_version }}

      - name: Install PyInstaller
        run: |
          pip install -U pyinstaller==${{ env.pyinstaller_version }}
          pip install -r ./src/launcher/requirements.txt

      - name: Build launcher
        run: >
          pyinstaller --specpath ./build 
          -p ./src/launcher --onefile --noconfirm
          -i ../src/launcher/icon.ico
          ./src/launcher/__main__.py

      - name: Rename executable
        run: |
          ${{ matrix.move-command }}

      - name: Create zip
        run: |
          ${{ matrix.zip-command }}

      - name: Upload Binary
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.artifact-name }}
          path: dist/ZenithProxy-${{ matrix.artifact-name }}.zip

      - name: Get Binary Hash
        run: ${{ matrix.sha1-command }} > ZenithProxy-${{ matrix.artifact-name }}.sha1

      - name: Upload Launcher Hash Version
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.artifact-name }}.sha1
          path: ZenithProxy-${{ matrix.artifact-name }}.sha1

  build-linux-aarch64:
    runs-on: 'ubuntu-latest'
    steps:
      - name: Check out repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build binary on emulated aarch64
        run: |
          docker run --rm --platform linux/arm64 --volume "${PWD}:/repo" python:${{ env.python_version }}-bullseye /bin/sh -c " \
            cd /repo && \
            python3 -m pip install -U pyinstaller==${{ env.pyinstaller_version }} && \
            python3 -m pip install -r ./src/launcher/requirements.txt && \
            pyinstaller --specpath ./build -p ./src/launcher --onefile --noconfirm ./src/launcher/__main__.py
          "

      - name: Rename executable
        run: |
          sudo mv dist/__main__ dist/launch

      - name: Elevate binary permissions
        run: sudo chmod +x dist/launch

      - name: Create zip
        run: sudo zip -j dist/ZenithProxy-launcher-linux-aarch64.zip dist/launch

      - name: Upload Binary
        uses: actions/upload-artifact@v4
        with:
          name: launcher-linux-aarch64
          path: dist/ZenithProxy-launcher-linux-aarch64.zip

      - name: Get Binary Hash
        run: sha1sum -b dist/launch | cut -d" " -f 1 > ZenithProxy-launcher-linux-aarch64.sha1

      - name: Upload Launcher Hash Version
        uses: actions/upload-artifact@v4
        with:
          name: launcher-linux-aarch64.sha1
          path: ZenithProxy-launcher-linux-aarch64.sha1

  package-python:
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Create Python executable zip
        run: zip -j launcher-py.zip src/launcher/*.py

      - name: Elevate script permissions
        run: |
          chmod +x src/launcher/launch.sh

      - name: Create release zip
        run: zip -j ZenithProxy-launcher-python.zip launcher-py.zip src/launcher/requirements.txt src/launcher/launch.sh src/launcher/launch.bat

      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: launcher-python
          path: ZenithProxy-launcher-python.zip

      - name: Get Binary Hash
        run: sha1sum -b launcher-py.zip | cut -d" " -f 1 > ZenithProxy-launcher-python.sha1

      - name: Upload Launcher Hash Version
        uses: actions/upload-artifact@v4
        with:
          name: launcher-python.sha1
          path: ZenithProxy-launcher-python.sha1

  # same as python package but with a custom bat script that bootstraps the python interpreter
  package-windows-python:
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Create Python executable zip
        run: zip -j launcher-py.zip src/launcher/*.py

      - name: Rename launch_windows.bat
        run: cp src/launcher/launch_windows.bat launch.bat

      - name: Create release zip
        run: zip -j ZenithProxy-launcher-windows-python-amd64.zip launcher-py.zip src/launcher/requirements.txt launch.bat scripts/windows_help.txt

      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: launcher-windows-python-amd64
          path: ZenithProxy-launcher-windows-python-amd64.zip

      - name: Get Binary Hash
        run: sha1sum -b launcher-py.zip | cut -d" " -f 1 > ZenithProxy-launcher-windows-python-amd64.sha1

      - name: Upload Launcher Hash Version
        uses: actions/upload-artifact@v4
        with:
          name: launcher-windows-python-amd64.sha1
          path: ZenithProxy-launcher-windows-python-amd64.sha1

  release-artifacts:
    needs:
      - build-mac
      - build-ubuntu20-x64
      - build-linux-aarch64
      - package-python
      - package-windows-python
    runs-on: ubuntu-latest
    steps:
      - name: Download job artifacts
        uses: actions/download-artifact@v4
        with:
          merge-multiple: true
          path: artifacts/

      - name: Compile Hashes
        run: |
          cat artifacts/*.sha1 > hashes.txt

      - name: Upload hashes.txt
        uses: actions/upload-artifact@v4
        with:
          name: hashes
          path: hashes.txt

      - name: Publish Release Artifacts
        uses: ncipollo/release-action@v1
        with:
          tag: "launcher-v3"
          artifacts: "artifacts/*.zip,hashes.txt"
          name: "Launcher V3"
          allowUpdates: true
          replacesArtifacts: true
          omitBodyDuringUpdate: true
          removeArtifacts: true
          makeLatest: true
