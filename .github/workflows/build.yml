# Note: does not build a jar file, only runs basic build and test tasks
name: ZenithProxy Build

on:
  pull_request:
  push:
    branches:
      - "1.21.4"

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Setup JDK
        uses: actions/setup-java@v4
        with:
          java-version: '24'
          distribution: 'temurin'

      - name: Elevate wrapper permissions
        run: chmod +x ./gradlew

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          dependency-graph: generate-and-submit

      - name: Build ZenithProxy
        run: ./gradlew build

      - name: Upload Java Artifact
        uses: actions/upload-artifact@v4
        with:
          name: ZenithProxy-java
          path: build/libs/ZenithProxy.jar
