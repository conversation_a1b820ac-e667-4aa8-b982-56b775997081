name: 1.21.4 PreRelease Build and Release

on:
  push:
    tags:
      - '[0-9]+.[0-9]+.[0-9]+\+1.21.4.pre'

jobs:
  build-java-and-linux-amd64:
    # 32gb memory needed for instrumenting
    runs-on: ubicloud-standard-8
    container:
      image: catthehacker/ubuntu:act-22.04
    steps:
      - name: Check out repository
        uses: actions/checkout@v4

      - name: Setup GraalVM
        uses: graalvm/setup-graalvm@v1
        with:
          java-version: '24'
          distribution: 'graalvm'
          github-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Echo Versions and Paths
        run: |
          echo "GRAALVM_HOME: $GRAALVM_HOME"
          echo "JAVA_HOME: $JAVA_HOME"
          java --version
          native-image --version

      - name: Elevate wrapper permissions
        run: chmod +x ./gradlew

      - name: Export Tag
        env:
          TAG_NAME: ${{ github.ref_name }}
        run: |
          echo "RELEASE_TAG=$TAG_NAME" >> "$GITHUB_ENV"

      - name: <PERSON>up Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          dependency-graph: generate-and-submit

      - name: Build ZenithProxy Java
        run: ./gradlew build

      - name: Upload Java Artifact
        uses: actions/upload-artifact@v4
        with:
          name: ZenithProxy-java
          path: build/libs/ZenithProxy.jar

      - name: Download GraalVM Profile
        run: |
          wget https://cdn.2b2t.vc/profile.iprof
          echo "GRAALVM_PGO_PATH=$(pwd)/profile.iprof" >> "$GITHUB_ENV"

#      - name: Enable GraalVM Profile Instrumenting
#        run: |
#          echo "GRAALVM_PGO_INSTRUMENT=true" >> "$GITHUB_ENV"

      - name: Build ZenithProxy Linux Native
        run: ./gradlew nativeCompile

      - name: Elevate binary permissions
        run: chmod +x build/native/nativeCompile/*

      - name: Zip Binary And Libs
        run: zip -j ZenithProxy.zip build/native/nativeCompile/*

      - name: Upload Linux Artifact
        uses: actions/upload-artifact@v4
        with:
          name: ZenithProxy-linux
          path: ZenithProxy.zip

      - name: Publish Maven Artifact Snapshot
        env:
          MAVEN_USERNAME: ${{ secrets.MAVEN_USERNAME }}
          MAVEN_PASSWORD: ${{ secrets.MAVEN_PASSWORD }}
        run: ./gradlew publishSnapshotPublicationToSnapshotsRepository

  release-artifacts:
    needs:
      - build-java-and-linux-amd64
    runs-on: ubuntu-latest
    steps:
      - name: Download job artifacts
        uses: actions/download-artifact@v4
        with:
          merge-multiple: true
          path: artifacts/

      - name: Get Release Tags
        env:
          TAG_NAME: ${{ github.ref_name }}
        run: |
          j_tag="$(sed 's/\+/\+java./' <<< $TAG_NAME)" 
          echo "JAVA_TAG=$j_tag" >> "$GITHUB_ENV"
          l_tag="$(sed 's/\+/\+linux./' <<< $TAG_NAME)"
          echo "LINUX_TAG=$l_tag" >> "$GITHUB_ENV"

      - name: Publish Java Artifact
        uses: ncipollo/release-action@v1
        with:
          tag: ${{ env.JAVA_TAG }}
          artifacts: "artifacts/*.jar"
          prerelease: true
          body: "File upload not for users. **Use the Launcher:** https://github.com/rfresh2/ZenithProxy/releases/tag/launcher-v3"
          commit: ${{ github.sha }}

      - name: Publish Linux Artifact
        uses: ncipollo/release-action@v1
        with:
          tag: ${{ env.LINUX_TAG }}
          artifacts: "artifacts/ZenithProxy.zip"
          prerelease: true
          body: "File upload not for users. **Use the Launcher:** https://github.com/rfresh2/ZenithProxy/releases/tag/launcher-v3"
          commit: ${{ github.sha }}
