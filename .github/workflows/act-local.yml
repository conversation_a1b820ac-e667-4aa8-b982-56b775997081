name: ACT-Local GraalVM Native

# Command:

# act workflow_dispatch -W .github\workflows\act-local.yml --artifact-server-path build\artifacts

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-22.04
    steps:
      - name: Check out repository
        uses: actions/checkout@v4

      - name: Setup GraalVM
        uses: graalvm/setup-graalvm@v1
        with:
          java-version: '24'
          distribution: 'graalvm'
          github-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Echo Versions and Paths
        run: |
          echo "GRAALVM_HOME: $GRAALVM_HOME"
          echo "JAVA_HOME: $JAVA_HOME"
          java --version
          native-image --version

      - name: Elevate wrapper permissions
        run: chmod +x ./gradlew

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          dependency-graph: generate-and-submit

      - name: Build ZenithProxy Java
        run: ./gradlew build
#
#      - name: Set ACT Runner Env Variables
#        run: |
#          export GRAALVM_HOME=/opt/hostedtoolcache/graalvm-jdk-20.0.2_linux-x64_bin/20.0.2/x64/graalvm-jdk-20.0.2+9.1
#          export JAVA_HOME=/opt/hostedtoolcache/graalvm-jdk-20.0.2_linux-x64_bin/20.0.2/x64/graalvm-jdk-20.0.2+9.1

      - name: Download GraalVM Profile
        run: |
          wget https://cdn.2b2t.vc/profile.iprof
          echo "GRAALVM_PGO_PATH=$(pwd)/profile.iprof" >> "$GITHUB_ENV"

      #      - name: Enable GraalVM Profile Instrumenting
      #        run: |
      #          echo "GRAALVM_PGO_INSTRUMENT=true" >> "$GITHUB_ENV"

      - name: Build ZenithProxy Linux Native
        run: ./gradlew nativeCompile

      - name: Elevate binary permissions
        run: chmod +x build/native/nativeCompile/*

      - name: Zip Binary And Libs
        run: zip -j ZenithProxy.zip build/native/nativeCompile/*

      - name: Upload Binary
        uses: actions/upload-artifact@v3
        with:
          name: linux-native
          path: ZenithProxy.zip
