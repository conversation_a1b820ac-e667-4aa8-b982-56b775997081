// Auto-Generated by ZenithProxy Data Generator
package com.zenith.mc.dimension;

import com.zenith.mc.DynamicRegistry;

import java.util.function.Supplier;

public final class DimensionRegistry {
    public static final DynamicRegistry<DimensionData> REGISTRY = new DynamicRegistry<DimensionData>(4);

    public static final Supplier<DimensionData> OVERWORLD = () -> REGISTRY.get("overworld");

    public static final Supplier<DimensionData> OVERWORLD_CAVES = () -> REGISTRY.get("overworld_caves");

    public static final Supplier<DimensionData> THE_END = () -> REGISTRY.get("the_end");

    public static final Supplier<DimensionData> THE_NETHER = () -> REGISTRY.get("the_nether");

    static {
        REGISTRY.register(new DimensionData(0, "overworld", -64, 320, 384));
        REGISTRY.register(new DimensionData(1, "overworld_caves", -64, 320, 384));
        REGISTRY.register(new DimensionData(2, "the_end", 0, 256, 256));
        REGISTRY.register(new DimensionData(3, "the_nether", 0, 256, 256));
    }
}
