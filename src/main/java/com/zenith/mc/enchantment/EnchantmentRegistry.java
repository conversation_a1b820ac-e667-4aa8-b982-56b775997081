// Auto-Generated by ZenithProxy Data Generator
package com.zenith.mc.enchantment;

import com.zenith.mc.DynamicRegistry;

import java.util.function.Supplier;

public final class EnchantmentRegistry {
    public static final DynamicRegistry<EnchantmentData> REGISTRY = new DynamicRegistry<EnchantmentData>(42);

    public static final Supplier<EnchantmentData> AQUA_AFFINITY = () -> REGISTRY.get("aqua_affinity");

    public static final Supplier<EnchantmentData> BANE_OF_ARTHROPODS = () -> REGISTRY.get("bane_of_arthropods");

    public static final Supplier<EnchantmentData> BINDING_CURSE = () -> REGISTRY.get("binding_curse");

    public static final Supplier<EnchantmentData> BLAST_PROTECTION = () -> REGISTRY.get("blast_protection");

    public static final Supplier<EnchantmentData> BREACH = () -> REGISTRY.get("breach");

    public static final Supplier<EnchantmentData> CHANNELING = () -> REGISTRY.get("channeling");

    public static final Supplier<EnchantmentData> DENSITY = () -> REGISTRY.get("density");

    public static final Supplier<EnchantmentData> DEPTH_STRIDER = () -> REGISTRY.get("depth_strider");

    public static final Supplier<EnchantmentData> EFFICIENCY = () -> REGISTRY.get("efficiency");

    public static final Supplier<EnchantmentData> FEATHER_FALLING = () -> REGISTRY.get("feather_falling");

    public static final Supplier<EnchantmentData> FIRE_ASPECT = () -> REGISTRY.get("fire_aspect");

    public static final Supplier<EnchantmentData> FIRE_PROTECTION = () -> REGISTRY.get("fire_protection");

    public static final Supplier<EnchantmentData> FLAME = () -> REGISTRY.get("flame");

    public static final Supplier<EnchantmentData> FORTUNE = () -> REGISTRY.get("fortune");

    public static final Supplier<EnchantmentData> FROST_WALKER = () -> REGISTRY.get("frost_walker");

    public static final Supplier<EnchantmentData> IMPALING = () -> REGISTRY.get("impaling");

    public static final Supplier<EnchantmentData> INFINITY = () -> REGISTRY.get("infinity");

    public static final Supplier<EnchantmentData> KNOCKBACK = () -> REGISTRY.get("knockback");

    public static final Supplier<EnchantmentData> LOOTING = () -> REGISTRY.get("looting");

    public static final Supplier<EnchantmentData> LOYALTY = () -> REGISTRY.get("loyalty");

    public static final Supplier<EnchantmentData> LUCK_OF_THE_SEA = () -> REGISTRY.get("luck_of_the_sea");

    public static final Supplier<EnchantmentData> LURE = () -> REGISTRY.get("lure");

    public static final Supplier<EnchantmentData> MENDING = () -> REGISTRY.get("mending");

    public static final Supplier<EnchantmentData> MULTISHOT = () -> REGISTRY.get("multishot");

    public static final Supplier<EnchantmentData> PIERCING = () -> REGISTRY.get("piercing");

    public static final Supplier<EnchantmentData> POWER = () -> REGISTRY.get("power");

    public static final Supplier<EnchantmentData> PROJECTILE_PROTECTION = () -> REGISTRY.get("projectile_protection");

    public static final Supplier<EnchantmentData> PROTECTION = () -> REGISTRY.get("protection");

    public static final Supplier<EnchantmentData> PUNCH = () -> REGISTRY.get("punch");

    public static final Supplier<EnchantmentData> QUICK_CHARGE = () -> REGISTRY.get("quick_charge");

    public static final Supplier<EnchantmentData> RESPIRATION = () -> REGISTRY.get("respiration");

    public static final Supplier<EnchantmentData> RIPTIDE = () -> REGISTRY.get("riptide");

    public static final Supplier<EnchantmentData> SHARPNESS = () -> REGISTRY.get("sharpness");

    public static final Supplier<EnchantmentData> SILK_TOUCH = () -> REGISTRY.get("silk_touch");

    public static final Supplier<EnchantmentData> SMITE = () -> REGISTRY.get("smite");

    public static final Supplier<EnchantmentData> SOUL_SPEED = () -> REGISTRY.get("soul_speed");

    public static final Supplier<EnchantmentData> SWEEPING_EDGE = () -> REGISTRY.get("sweeping_edge");

    public static final Supplier<EnchantmentData> SWIFT_SNEAK = () -> REGISTRY.get("swift_sneak");

    public static final Supplier<EnchantmentData> THORNS = () -> REGISTRY.get("thorns");

    public static final Supplier<EnchantmentData> UNBREAKING = () -> REGISTRY.get("unbreaking");

    public static final Supplier<EnchantmentData> VANISHING_CURSE = () -> REGISTRY.get("vanishing_curse");

    public static final Supplier<EnchantmentData> WIND_BURST = () -> REGISTRY.get("wind_burst");

    static {
        REGISTRY.register(new EnchantmentData(0, "aqua_affinity"));
        REGISTRY.register(new EnchantmentData(1, "bane_of_arthropods"));
        REGISTRY.register(new EnchantmentData(2, "binding_curse"));
        REGISTRY.register(new EnchantmentData(3, "blast_protection"));
        REGISTRY.register(new EnchantmentData(4, "breach"));
        REGISTRY.register(new EnchantmentData(5, "channeling"));
        REGISTRY.register(new EnchantmentData(6, "density"));
        REGISTRY.register(new EnchantmentData(7, "depth_strider"));
        REGISTRY.register(new EnchantmentData(8, "efficiency"));
        REGISTRY.register(new EnchantmentData(9, "feather_falling"));
        REGISTRY.register(new EnchantmentData(10, "fire_aspect"));
        REGISTRY.register(new EnchantmentData(11, "fire_protection"));
        REGISTRY.register(new EnchantmentData(12, "flame"));
        REGISTRY.register(new EnchantmentData(13, "fortune"));
        REGISTRY.register(new EnchantmentData(14, "frost_walker"));
        REGISTRY.register(new EnchantmentData(15, "impaling"));
        REGISTRY.register(new EnchantmentData(16, "infinity"));
        REGISTRY.register(new EnchantmentData(17, "knockback"));
        REGISTRY.register(new EnchantmentData(18, "looting"));
        REGISTRY.register(new EnchantmentData(19, "loyalty"));
        REGISTRY.register(new EnchantmentData(20, "luck_of_the_sea"));
        REGISTRY.register(new EnchantmentData(21, "lure"));
        REGISTRY.register(new EnchantmentData(22, "mending"));
        REGISTRY.register(new EnchantmentData(23, "multishot"));
        REGISTRY.register(new EnchantmentData(24, "piercing"));
        REGISTRY.register(new EnchantmentData(25, "power"));
        REGISTRY.register(new EnchantmentData(26, "projectile_protection"));
        REGISTRY.register(new EnchantmentData(27, "protection"));
        REGISTRY.register(new EnchantmentData(28, "punch"));
        REGISTRY.register(new EnchantmentData(29, "quick_charge"));
        REGISTRY.register(new EnchantmentData(30, "respiration"));
        REGISTRY.register(new EnchantmentData(31, "riptide"));
        REGISTRY.register(new EnchantmentData(32, "sharpness"));
        REGISTRY.register(new EnchantmentData(33, "silk_touch"));
        REGISTRY.register(new EnchantmentData(34, "smite"));
        REGISTRY.register(new EnchantmentData(35, "soul_speed"));
        REGISTRY.register(new EnchantmentData(36, "sweeping_edge"));
        REGISTRY.register(new EnchantmentData(37, "swift_sneak"));
        REGISTRY.register(new EnchantmentData(38, "thorns"));
        REGISTRY.register(new EnchantmentData(39, "unbreaking"));
        REGISTRY.register(new EnchantmentData(40, "vanishing_curse"));
        REGISTRY.register(new EnchantmentData(41, "wind_burst"));
    }
}
