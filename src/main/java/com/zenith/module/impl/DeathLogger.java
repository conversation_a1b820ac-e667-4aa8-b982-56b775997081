package com.zenith.module.impl;

import com.github.rfresh2.EventConsumer;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.zenith.event.chat.DeathMessageChatEvent;
import com.zenith.feature.deathmessages.DeathMessageParseResult;
import com.zenith.feature.deathmessages.Killer;
import com.zenith.feature.deathmessages.KillerType;
import com.zenith.module.api.Module;
import com.zenith.util.ComponentSerializer;
import lombok.Getter;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

import static com.github.rfresh2.EventConsumer.of;
import static com.zenith.Globals.*;

/**
 * DeathLogger module - Records all player death messages to console and JSON file
 * 
 * Features:
 * - Logs detailed death information in JSON format
 * - Configurable console output
 * - Saves to local JSON file as array
 * - Includes timestamps, coordinates (optional), weapon info, killer details
 * - Pretty-printed JSON output option
 */
public class DeathLogger extends Module {
    
    private static final String DEATHS_DIRECTORY = "deaths";
    private final Gson gson;
    private final Gson prettyGson;
    
    @Getter
    public static class DeathLogEntry {
        private final String timestamp;
        private final String victim;
        private final String killer;
        private final String killerType;
        private final String weapon;
        private final String message;
        private final String rawMessage;
        private final String componentJson;
        private final CoordinateInfo coordinates;
        
        public DeathLogEntry(DeathMessageParseResult deathMessage, String message, String componentJson) {
            this.timestamp = Instant.now().atOffset(ZoneOffset.UTC)
                .format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);
            this.victim = deathMessage.victim();
            
            Optional<Killer> killerOpt = deathMessage.killer();
            this.killer = killerOpt.map(Killer::name).orElse(null);
            this.killerType = killerOpt.map(k -> k.type().name()).orElse(null);
            
            this.weapon = deathMessage.weapon().orElse(null);
            this.message = message;
            this.rawMessage = ComponentSerializer.serializePlain(
                ComponentSerializer.deserialize(componentJson)
            );
            this.componentJson = componentJson;
            
            // Include coordinates if enabled and available
            if (CONFIG.client.extra.deathLogger.includeCoordinates && CACHE.getPlayerCache().getThePlayer() != null) {
                var player = CACHE.getPlayerCache().getThePlayer();
                this.coordinates = new CoordinateInfo(
                    (int) player.getX(),
                    (int) player.getY(), 
                    (int) player.getZ(),
                    getCurrentDimensionName()
                );
            } else {
                this.coordinates = null;
            }
        }
        
        private String getCurrentDimensionName() {
            try {
                return CACHE.getChunkCache().getCurrentDimension().name();
            } catch (Exception e) {
                return "UNKNOWN";
            }
        }
    }
    
    @Getter
    public static class CoordinateInfo {
        private final int x;
        private final int y;
        private final int z;
        private final String dimension;
        
        public CoordinateInfo(int x, int y, int z, String dimension) {
            this.x = x;
            this.y = y;
            this.z = z;
            this.dimension = dimension;
        }
    }
    
    public DeathLogger() {
        super();
        this.gson = new Gson();
        this.prettyGson = new GsonBuilder()
            .setPrettyPrinting()
            .disableHtmlEscaping()
            .create();
        
        // Create deaths directory if it doesn't exist
        try {
            Path deathsPath = Paths.get(DEATHS_DIRECTORY);
            if (!Files.exists(deathsPath)) {
                Files.createDirectories(deathsPath);
                info("Created deaths directory: " + deathsPath.toAbsolutePath());
            }
        } catch (IOException e) {
            error("Failed to create deaths directory", e);
        }
    }
    
    @Override
    public List<EventConsumer<?>> registerEvents() {
        return List.of(
            of(DeathMessageChatEvent.class, this::handleDeathMessage)
        );
    }
    
    @Override
    public boolean enabledSetting() {
        return CONFIG.client.extra.deathLogger.enabled;
    }
    
    private void handleDeathMessage(DeathMessageChatEvent event) {
        try {
            DeathMessageParseResult deathMessage = event.deathMessage();
            String message = event.message();
            String componentJson = ComponentSerializer.serializeJson(event.component());
            
            // Create death log entry
            DeathLogEntry logEntry = new DeathLogEntry(deathMessage, message, componentJson);
            
            // Print to console if enabled
            if (CONFIG.client.extra.deathLogger.printToConsole) {
                printDeathToConsole(logEntry);
            }
            
            // Save to file if enabled
            if (CONFIG.client.extra.deathLogger.saveToFile) {
                saveDeathToFile(logEntry);
            }
            
        } catch (Exception e) {
            error("Error processing death message", e);
        }
    }
    
    private void printDeathToConsole(DeathLogEntry logEntry) {
        try {
            Gson outputGson = CONFIG.client.extra.deathLogger.prettyPrintJson ? prettyGson : gson;
            String jsonOutput = outputGson.toJson(logEntry);
            
            info("=== DEATH MESSAGE LOGGED ===");
            info("Victim: {}", logEntry.getVictim());
            if (logEntry.getKiller() != null) {
                info("Killer: {} ({})", logEntry.getKiller(), logEntry.getKillerType());
            }
            if (logEntry.getWeapon() != null) {
                info("Weapon: {}", logEntry.getWeapon());
            }
            info("Message: {}", logEntry.getMessage());
            if (logEntry.getCoordinates() != null) {
                var coords = logEntry.getCoordinates();
                info("Coordinates: {} {} {} ({})", coords.getX(), coords.getY(), coords.getZ(), coords.getDimension());
            }
            info("Timestamp: {}", logEntry.getTimestamp());
            info("JSON: {}", jsonOutput);
            info("============================");
            
        } catch (Exception e) {
            error("Error printing death to console", e);
        }
    }
    
    private void saveDeathToFile(DeathLogEntry logEntry) {
        try {
            String fileName = CONFIG.client.extra.deathLogger.fileName;
            Path filePath = Paths.get(DEATHS_DIRECTORY, fileName);
            
            // Read existing data or create new array
            JsonArray deathsArray;
            if (Files.exists(filePath)) {
                try (Reader reader = Files.newBufferedReader(filePath)) {
                    deathsArray = gson.fromJson(reader, JsonArray.class);
                    if (deathsArray == null) {
                        deathsArray = new JsonArray();
                    }
                } catch (Exception e) {
                    warn("Error reading existing death log file, creating new array: {}", e.getMessage());
                    deathsArray = new JsonArray();
                }
            } else {
                deathsArray = new JsonArray();
            }
            
            // Add new death entry
            JsonObject entryJson = gson.toJsonTree(logEntry).getAsJsonObject();
            deathsArray.add(entryJson);
            
            // Write back to file
            Gson outputGson = CONFIG.client.extra.deathLogger.prettyPrintJson ? prettyGson : gson;
            try (Writer writer = Files.newBufferedWriter(filePath)) {
                outputGson.toJson(deathsArray, writer);
            }
            
            debug("Death logged to file: {} (total entries: {})", filePath, deathsArray.size());
            
        } catch (Exception e) {
            error("Error saving death to file", e);
        }
    }
    
    @Override
    public void onEnable() {
        info("DeathLogger enabled - will log deaths to: {}", 
            CONFIG.client.extra.deathLogger.saveToFile ? 
                Paths.get(DEATHS_DIRECTORY, CONFIG.client.extra.deathLogger.fileName).toAbsolutePath() : 
                "console only");
    }
    
    @Override
    public void onDisable() {
        info("DeathLogger disabled");
    }
}
