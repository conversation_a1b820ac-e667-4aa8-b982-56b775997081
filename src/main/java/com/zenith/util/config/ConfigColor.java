package com.zenith.util.config;

import net.dv8tion.jda.api.utils.Color;

public enum ConfigColor {
    WHITE(Color.WHITE),
    LIGHT_GRAY(Color.LIGHT_GRAY),
    GRAY(Color.GRAY),
    <PERSON>AR<PERSON>_GRAY(Color.DARK_GRAY),
    <PERSON><PERSON><PERSON><PERSON>(Color.BLACK),
    RED(Color.RED),
    <PERSON><PERSON><PERSON>(Color.PINK),
    ORANGE(Color.ORANGE),
    <PERSON>ELLOW(Color.YELLOW),
    <PERSON>RE<PERSON>(Color.GREEN),
    MAGENTA(Color.MAGENTA),
    CYAN(Color.CYAN),
    BLUE(Color.BLUE),
    LIGHT_SEA_GREEN(Color.LIGHT_SEA_GREEN),
    <PERSON><PERSON><PERSON>_SEA_GREEN(Color.MEDIUM_SEA_GREEN),
    SUMMER_SKY(Color.SUMMER_SKY),
    DEEP_LILAC(Color.DEEP_LILAC),
    RUBY(Color.RUBY),
    <PERSON>O<PERSON>_YELLOW(Color.MOON_YELLOW),
    TA<PERSON><PERSON><PERSON>_GOLD(Color.TAHITI_GOLD),
    <PERSON><PERSON>NABA<PERSON>(Color.CINNABAR),
    <PERSON><PERSON><PERSON>ARIN<PERSON>(Color.SUBMARINE),
    HOKI(Color.HOKI),
    DEEP_SEA(Color.DEEP_SEA),
    SEA_GREEN(Color.SEA_GREEN),
    ENDEAVOUR(Color.ENDEAVOUR),
    VIVID_VIOLET(Color.VIVID_VIOLET),
    JAZZBERRY_JAM(Color.JAZZBERRY_JAM),
    DARK_GOLDENROD(Color.DARK_GOLDENROD),
    RUST(Color.RUST),
    BROWN(Color.BROWN),
    GRAY_CHATEAU(Color.GRAY_CHATEAU),
    BISMARK(Color.BISMARK);

    private final Color discordColor;

    ConfigColor(Color discordColor) {
        this.discordColor = discordColor;
    }

    public Color discord() {
        return discordColor;
    }

    public int rgb() {
        return discordColor.getRGB();
    }

    public com.zenith.util.Color color() {
        return com.zenith.util.Color.fromInt(discordColor.getRGB());
    }
}
