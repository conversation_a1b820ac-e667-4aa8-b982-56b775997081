package com.zenith.util.config;

import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import com.zenith.feature.chatschema.ChatSchema;
import com.zenith.feature.waypoints.Waypoint;
import com.zenith.feature.whitelist.PlayerEntry;
import com.zenith.module.impl.ActiveHours.ActiveTime;
import it.unimi.dsi.fastutil.ints.IntArraySet;
import lombok.Getter;
import org.geysermc.mcprotocollib.network.ProxyInfo;
import org.geysermc.mcprotocollib.protocol.data.game.entity.type.EntityType;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedHashMap;


public final class Config {
    public final String warning = "DO NOT EDIT THIS FILE MANUALLY. Use commands to configure settings";
    public final Authentication authentication = new Authentication();
    public final Client client = new Client();
    public final Debug debug = new Debug();
    public final Server server = new Server();
    public final Plugins plugins = new Plugins();
    public final InteractiveTerminal interactiveTerminal = new InteractiveTerminal();
    public final InGameCommands inGameCommands = new InGameCommands();
    public final Theme theme = new Theme();
    public final Discord discord = new Discord();
    public final Database database = new Database();
    public final AutoUpdater autoUpdater = new AutoUpdater();

    public static final class Authentication {
        public AccountType accountType = AccountType.DEVICE_CODE;
        // only used for MSA
        public String email = "<EMAIL>";
        public String password = "abc123";
        // updated on successful login
        public String username = "Unknown";
        public boolean prio = false;
        public boolean prioBanned = false;
        public boolean authTokenRefresh = true;
        public int msaLoginAttemptsBeforeCacheWipe = 2;
        public boolean openBrowserOnLogin = true;
        public boolean alwaysRefreshOnLogin = false;
        public int maxRefreshIntervalMins = 360; // 6 hrs
        public boolean useClientConnectionProxy = false;

        public enum AccountType {
            @SerializedName("msa") MSA,
            @SerializedName("device_code") DEVICE_CODE,
            @SerializedName("device_code_without_device_token") DEVICE_CODE_WITHOUT_DEVICE_TOKEN,
            @SerializedName("prism") PRISM,
            @SerializedName("offline") OFFLINE
        }
    }

    public static final class Theme {
        public ConfigColor primary = ConfigColor.CYAN;
        public ConfigColor success = ConfigColor.MEDIUM_SEA_GREEN;
        public ConfigColor error = ConfigColor.RUBY;
        public ConfigColor inQueue = ConfigColor.MOON_YELLOW;
    }

    public static final class Client {
        public final Server server = new Server();
        public final ConnectionProxy connectionProxy = new ConnectionProxy();
        public int compressionLevel = -1;
        public boolean autoConnect = false; // auto-connect proxy on process start
        public final ClientViaVersion viaversion = new ClientViaVersion();
        public String bindAddress = "0.0.0.0";
        public boolean maxPlaytimeReconnect = false;
        public long maxPlaytimeReconnectMins = 1440;
        public boolean automaticKeepAliveManagement = true;
        public int defaultClientRenderDistance = 25;
        public final ClientTimeout timeout = new ClientTimeout();
        public final Ping ping = new Ping();
        public final ChatSigning chatSigning = new ChatSigning();
        public final Extra extra = new Extra();
        public final Inventory inventory = new Inventory();
        public final ChatSchemas chatSchemas = new ChatSchemas();

        public static final class ChatSchemas {
            public LinkedHashMap<String, ChatSchema> serverSchemas = new LinkedHashMap<>();
        }

        public static final class Inventory {
            public int actionDelayTicks = 5;
            public boolean autoCloseOpenContainers = true;
            public int autoCloseOpenContainerAfterSeconds = 60;
            public boolean ncpStrict = false;
        }

        public static final class ChatSigning {
            public boolean enabled = true;
            public boolean force = false;
            public boolean signWhispers = true;
        }

        public static final class ClientViaVersion {
            public boolean enabled = false;
            public boolean disableOn2b2t = true;
            public boolean autoProtocolVersion = true;
            public int protocolVersion = 765;
        }

        public static final class ClientTimeout {
            public boolean enable = true;
            public int seconds = 60;
        }

        public static final class Ping {
            public Mode mode = Mode.TABLIST;
            public int packetPingIntervalSeconds = 10;

            public enum Mode {
                TABLIST, PACKET
            }
        }

        public static final class Extra {
            public final AntiAFK antiafk = new AntiAFK();
            public final Spook spook = new Spook();
            public final Utility utility = new Utility();
            public final AutoReconnect autoReconnect = new AutoReconnect();
            public final AutoRespawn autoRespawn = new AutoRespawn();
            public final Spammer spammer = new Spammer();
            public final AutoReply autoReply = new AutoReply();
            public final Stalk stalk = new Stalk();
            public final AutoEat autoEat = new AutoEat();
            public final AutoFish autoFish = new AutoFish();
            public final KillAura killAura = new KillAura();
            public final AutoTotem autoTotem = new AutoTotem();
            public final AntiLeak antiLeak = new AntiLeak();
            public final Chat chat = new Chat();
            public final AntiKick antiKick = new AntiKick();
            public final ReplayMod replayMod = new ReplayMod();
            public final ArrayList<PlayerEntry> friendsList = new ArrayList<>();
            public boolean autoConnectOnLogin = true;
            public boolean prioStatusChangeMention = true;
            public boolean killMessage = true;
            public boolean logChatMessages = true;
            public boolean logOnlyQueuePositionUpdates = true;
            public final CoordObfuscation coordObfuscation = new CoordObfuscation();
            public final ActionLimiter actionLimiter = new ActionLimiter();
            public final VisualRange visualRange = new VisualRange();
            public final AutoArmor autoArmor = new AutoArmor();
            public final AutoMend autoMend = new AutoMend();
            public final QueueWarning queueWarning = new QueueWarning();
            public final Click click = new Click();
            public final SessionTimeLimit sessionTimeLimit = new SessionTimeLimit();
            public final AutoOmen autoOmen = new AutoOmen();
            public final Pathfinder pathfinder = new Pathfinder();
            public final SpawnPatrol spawnPatrol = new SpawnPatrol();
            public final PearlLoader pearlLoader = new PearlLoader();
            public final Waypoints waypoints = new Waypoints();
            public final DeathLogger deathLogger = new DeathLogger();
            public String whisperCommand = "msg";

            public static final class Waypoints {
                public ArrayList<Waypoint> waypoints = new ArrayList<>();
            }

            public static final class PearlLoader {
                public ArrayList<Pearl> pearls = new ArrayList<>();
                public boolean returnToStartPos = true;

                /**
                 * @param id player name or some other unique identifier
                 * @param x  the position of the block we need to interact with to load the pearl
                 */
                public record Pearl(String id, int x, int y, int z) { }
            }

            public static final class Pathfinder {
                public boolean allowBreak = true;
                public boolean allowSprint = false;
                public boolean allowPlace = true;
                public boolean allowInventory = true;
                public boolean allowDownward = true;
                public boolean allowParkour = false;
                public boolean allowParkourPlace = false;
                public boolean allowParkourAscend = false;
                public boolean allowDiagonalDescend = false;
                public boolean allowDiagonalAscend = false;
                public boolean diagonalCentering = false;
                public boolean traverseCentering = false;
                public double blockBreakAdditionalCost = 2;
                public int maxFallHeightNoWater = 3;
                public boolean allowLongFall = false;
                public double longFallCostLogMultiplier = 50;
                public double longFallCostAddCost = 100;
                public int followRadius = 2;
                public int teleportDelayMs = 500;
                public boolean renderPath = true;
                public int pathRenderIntervalTicks = 10;
                public boolean renderPathDetailed = false;
                public int primaryTimeoutMs = 500;
                public int failureTimeoutMs = 2000;
                public int planAheadPrimaryTimeoutMs = 4000;
                public int planAheadFailureTimeoutMs = 5000;
                public int failedPathSearchCooldownMs = 1000;
                public boolean getToBlockExploreForBlocks = true;
                public boolean getToBlockBlacklistClosestOnFailure = false;
                public boolean simplifyUnloadedYGoal = false;
            }

            public static class SessionTimeLimit {
                public boolean enabled = true;
                public IntArraySet ingameNotificationPositions = IntArraySet.of(1, 2, 3, 4, 5, 6, 7, 8, 9, 10);
                public IntArraySet discordNotificationPositions = new IntArraySet();
                public IntArraySet discordMentionPositions = new IntArraySet();
                public boolean dynamic2b2tSessionTimeLimit = true;
            }

            public static class Click {
                public boolean enabled = true;
                public boolean holdLeftClick = false;
                public boolean holdRightClick = false;
                public boolean hasRotation = false;
                public boolean holdSneak = false;
                public float rotationYaw = 0;
                public float rotationPitch = 0;
                public HoldRightClickMode holdRightClickMode = HoldRightClickMode.MAIN_HAND;
                public HoldClickTarget holdClickTarget = HoldClickTarget.ANY;
                public int holdRightClickInterval = 5;
                public int holdLeftClickInterval = 0;
                public float additionalBlockReach = 0;
                public float additionalEntityReach = 0;
                public enum HoldRightClickMode {
                    MAIN_HAND,
                    OFF_HAND,
                    ALTERNATE_HANDS
                }
                public enum HoldClickTarget {
                    ANY,
                    NONE,
                    ENTITY,
                    BLOCK
                }
            }

            public static class SpawnPatrol {
                public boolean enabled = false;
                public boolean ignoreFriends = true;
                public boolean targetOnlyNakeds = true;
                public boolean targetAttackers = true;
                public boolean stickyTargeting = true;
                public boolean nether = true;
                public boolean stuckKill = true;
                public int stuckKillSeconds = 60;
                public int stuckKillMinDist = 10;
                public boolean stuckKillAntiStuck = true;
                public int goalX = 0;
                public int goalY = 120;
                public int goalZ = 0;
                public int maxPatrolRange = 500;
                public final ArrayList<PlayerEntry> ignoreList = new ArrayList<>();
            }

            public static final class QueueWarning {
                public boolean enabled = true;
                public IntArraySet warningPositions = IntArraySet.of(1, 2, 3, 10);
                public IntArraySet mentionPositions = new IntArraySet();
            }

            public static class AutoMend {
                public boolean enabled = false;
            }

            public static class VisualRange {
                public boolean enabled = true;
                public boolean ignoreFriends = false;
                public boolean enterAlert = true;
                public boolean enterAlertMention = true;
                public boolean leaveAlert = true;
                public boolean logoutAlert = true;
                public boolean enterWhisper = false;
                public String enterWhisperMessage = "Hello, I am using ZenithProxy! I have alerted my owner that you are here!";
                public int enterWhisperCooldownSeconds = 30;
                public boolean enterWhisperWhilePlayerConnected = false;
                public boolean replayRecording = false;
                public ReplayRecordingMode replayRecordingMode = ReplayRecordingMode.ENEMY;
                public int replayRecordingCooldownMins = 5;
                public enum ReplayRecordingMode {
                    ENEMY,
                    ALL
                }
            }

            public static class AutoArmor {
                public boolean enabled = false;
            }

            public static class AntiKick {
                public boolean enabled = false;
                public int playerInactivityKickMins = 15;
                public int minWalkDistance = 2;
            }

            public static final class Chat {
                public boolean enabled = true;
                public final ArrayList<PlayerEntry> ignoreList = new ArrayList<>();
                public boolean hideChat = false;
                public boolean hideWhispers = false;
                public boolean hideDeathMessages = false;
                public boolean showConnectionMessages = false;
            }

            public static final class AutoTotem {
                public boolean enabled = true;
                public boolean inGame = false;
                public int healthThreshold = 20;
                public boolean noTotemsAlert = false;
                public boolean noTotemsAlertMention = false;
                public boolean totemPopAlert = false;
                public boolean totemPopAlertMention = false;
            }

            public static final class AntiLeak {
                public boolean enabled = true;
                // checks if numbers in chat are within a range from your coords
                public boolean rangeCheck = true;
                // the factor to divide and multiply your coords by to get the range
                public double rangeFactor = 10.0;
            }

            public static final class KillAura {
                public boolean enabled = false;
                public boolean targetPlayers = false;
                public boolean targetHostileMobs = true;
                public boolean targetNeutralMobs = false;
                public boolean targetCustom = false;
                public boolean onlyNeutralAggressive = false;
                public boolean onlyHostileAggressive = false;
                public boolean switchWeapon = true;
                public boolean targetArmorStands = false;
                public int attackDelayTicks = 10;
                public final ArrayList<EntityType> customTargets = new ArrayList<>();
                public Priority priority = Priority.NONE;

                public enum Priority {
                    NONE,
                    NEAREST
                }
            }

            public static final class AutoEat {
                public boolean enabled = true;
                public int healthThreshold = 10;
                public int hungerThreshold = 10;
                public boolean warning = true;
                public boolean warningMention = false;
                public boolean allowUnsafeFood = false;
            }

            public static final class AutoOmen {
                public boolean enabled = false;
                public boolean ignoreRaidStatus = false;
            }

            public static final class Stalk {
                public boolean enabled = false;
                public final ArrayList<PlayerEntry> stalking = new ArrayList<>();
            }

            public static final class AutoFish {
                public boolean enabled = false;
                public float yaw = 0.0f;
                public float pitch = 0.0f;
            }

            public static final class AntiAFK {
                public Actions actions = new Actions();
                public boolean enabled = true;

                public static final class Actions {
                    public boolean walk = true;
                    // we only need about 5-6 blocks in reality but adding a few extra here to be safe
                    // this isn't dependent on chunks loading but is more based on distance
                    public int walkDistance = 8;
                    // avoid going off ledges even when falls are non-fatal
                    public boolean safeWalk = true;
                    public long walkDelayTicks = 400;
                    public boolean swingHand = true;
                    public long swingDelayTicks = 3000;
                    public boolean rotate = true;
                    public long rotateDelayTicks = 300L;
                    public boolean jump = false;
                    public boolean jumpOnlyInWater = true;
                    public long jumpDelayTicks = 1L;
                    public boolean sneak = false;
                }
            }

            public static final class Spook {
                public boolean enabled = false;
                public TargetingMode spookTargetingMode = TargetingMode.VISUAL_RANGE;

                public enum TargetingMode {
                    NEAREST,
                    VISUAL_RANGE
                }
            }

            public static final class ReplayMod {
                public boolean sendRecordingsToDiscord = false;
                public int maxRecordingTimeMins = 0;
                public AutoRecordMode autoRecordMode = AutoRecordMode.NONE;
                public int replayRecordingHealthThreshold = 5;
                public boolean fileIOUploadIfTooLarge = true;

                @Getter
                public enum AutoRecordMode {
                    NONE("off"),
                    PROXY_CONNECTED("proxyConnected"),
                    PLAYER_CONNECTED("playerConnected"),
                    HEALTH("health");
                    private final String name;

                    AutoRecordMode(String name) {
                        this.name = name;
                    }

                    public static String[] names() {
                        return Arrays.stream(AutoRecordMode.values()).map(AutoRecordMode::getName).toArray(String[]::new);
                    }
                }
            }

            public static final class Utility {
                public final Actions actions = new Actions();

                public static final class Actions {
                    public final AutoDisconnect autoDisconnect = new AutoDisconnect();
                    public final ActiveHours activeHours = new ActiveHours();
                }

                public static final class AutoDisconnect {
                    public boolean enabled = false;
                    public boolean healthDisconnect = true;
                    public boolean whilePlayerConnected = false;
                    public boolean autoClientDisconnect = false;
                    public int health = 5;
                    public boolean thunder = false;
                    public boolean cancelAutoReconnect = true;
                    // checks friends list, whitelist, and spectator whitelist
                    public boolean onUnknownPlayerInVisualRange = false;
                    public boolean mentionOnDisconnect = false;
                    public boolean onTotemPop = false;
                    public int minTotemsRemaining = 50;
                }

                public static final class ActiveHours {
                    public boolean enabled = false;
                    public boolean forceReconnect = false;
                    public boolean queueEtaCalc = true;
                    public boolean fullSessionUntilNextDisconnect = true;
                    public String timeZoneId = "Universal";
                    public final ArrayList<ActiveTime> activeTimes = new ArrayList<>();
                }
            }

            public static final class AutoReconnect {
                public boolean enabled = true;
                public int delaySeconds = 5;
                public int maxAttempts = 200;
            }

            public static final class AutoRespawn {
                public boolean enabled = true;
                public int delayMillis = 100;
            }

            public static final class Spammer {
                public boolean enabled = false;
                public boolean whilePlayerConnected = false;
                public boolean whisper = false;
                public long delayTicks = 200;
                public boolean randomOrder = false;
                public boolean appendRandom = false;
                public final ArrayList<String> messages = Lists.newArrayList(
                    "ZenithProxy on top!",
                    "I just skipped queue thanks to ZenithProxy!",
                    "Download ZenithProxy on GitHub today! It's free!"
                );
            }

            public static final class AutoReply {
                public boolean enabled = false;
                public int cooldownSeconds = 15;
                public String message = "I am currently AFK, check back later or message me on discord.";
            }

            public static final class CoordObfuscation {
                // all offsets in chunk coords
                public boolean enabled = false;
                public boolean validateSetup = true;
                public boolean exemptProxyAccount = false;
                public ObfuscationMode mode = ObfuscationMode.RANDOM_OFFSET;
                public boolean obfuscateBedrock = true;
                public boolean obfuscateChunkHeightmap = true;
                public boolean obfuscateChunkLighting = true;
                public boolean obfuscateBiomes = false;
                public String obfuscateBiomesKey = "plains";
                public int teleportOffsetRegenerateDistanceMin = 64; // minimum distance to regenerate coords at
                public int randomMinDistanceFromSelf = 100_000;
                public int randomMinDistanceFromSpawn = 100_000;
                public int randomMaxDistanceFromSpawn = 29_000_000;
                public int constantOffsetX = 0;
                public int constantOffsetZ = 0;
                public boolean constantOffsetNetherTranslate = true;
                public int constantOffsetMinSpawnDistance = 100000; // min distance to spawn the actual coords are before player is disconnected
                public int atLocationX = 0;
                public int atLocationZ = 0;
                public int delayPlayerLoginsAfterTpMs = 1000;
                public boolean disconnectWhileEyeOfEnderPresent = true;
                public boolean debugPacketLog = false;
                public boolean disconnectWhileNearOffsetBlocks = true;
                public enum ObfuscationMode {
                    RANDOM_OFFSET,
                    CONSTANT_OFFSET,
                    AT_LOCATION
                }
            }

            public static class ActionLimiter {
                public boolean enabled = false;
                // be careful with this, auto respawn will still respawn after they disconnect
                //  there is a position check at login so it should be ok, but the respawn will still go through
                public boolean allowRespawn = true;
                public boolean allowMovement = true;
                public int movementDistance = 1000; // distance from home coords
                public int movementHomeX = 0;
                public int movementHomeZ = 0;
                public int movementMinY = -64;
                public boolean allowEnderChest = true;
                public boolean allowBlockBreaking = true;
                // todo: dunno how to block this but still allow other interactions
//                public boolean allowBlockPlacing = true;
                public boolean allowInventory = true;
                public boolean allowUseItem = true;
                public boolean allowBookSigning = true;
                public boolean allowInteract = true;
                public boolean allowChat = true; // outbound chats
                public boolean allowServerCommands = true; // includes whispers
                public boolean exemptProxyAccount = false;
                public boolean itemsBlacklistEnabled = false;
                public final HashSet<String> itemsBlacklist = new HashSet<>();
            }

            public static final class DeathLogger {
                public boolean enabled = false;
                public boolean printToConsole = true;
                public boolean saveToFile = true;
                public String fileName = "death_logs.json";
                public boolean includeTimestamp = true;
                public boolean includeCoordinates = false;
                public boolean includeWeaponInfo = true;
                public boolean includeKillerType = true;
                public boolean prettyPrintJson = true;
            }
        }

        public static final class Server {
            public String address = "connect.2b2t.org";
            public int port = 25565;
        }

        public static final class ConnectionProxy {
            public boolean enabled = false;
            public ProxyInfo.Type type = ProxyInfo.Type.SOCKS5;
            public String host = "127.0.0.1";
            public int port = 7890;
            public String user = "";
            public String password = "";
        }
    }

    public static final class Debug {
        public final PacketLog packetLog = new PacketLog();
        public final Server server = new Server();
        public boolean clearOldLogs = false;
        public boolean kickDisconnect = false;
        public boolean debugLogs = false;
        public boolean terminalDebugLogs = false;
        public boolean inventorySyncOnLogin = false;

        public static final class PacketLog {
            public boolean enabled = false;
            public boolean logLevelDebug = true;
            public PacketLogConfig clientPacketLog = new PacketLogConfig();
            public PacketLogConfig serverPacketLog = new PacketLogConfig();
            // todo: could be more flexible, but this can cover the most basic use cases
            public String packetFilter = "";

            public static final class PacketLogConfig {
                public boolean received = false;
                public boolean receivedBody = false;
                public boolean preSent = false;
                public boolean preSentBody = false;
                public boolean postSent = false;
                public boolean postSentBody = false;
            }
        }

        public static final class Server {
            public final Cache cache = new Cache();

            public static final class Cache {
                public boolean unlockAllRecipes = true;
                public boolean fullbrightChunkSkylight = true;
                public boolean fullbrightChunkBlocklight = false;
            }
        }
    }

    public static final class Server {
        public final Bind bind = new Bind();
        public int compressionThreshold = 256;
        public int compressionLevel = -1;
        public boolean enabled = true;
        public final Extra extra = new Extra();
        public final Ping ping = new Ping();
        public final ServerViaVersion viaversion = new ServerViaVersion();
        public boolean verifyUsers = true;
        public boolean enforceMatchingConnectingAddress = false;
        public boolean acceptTransfers = true;
        public boolean onlyZenithTransfers = true;
        public String proxyIP = "localhost";
        public int queueStatusRefreshMinutes = 5; // how often to refresh queue lengths
        public boolean dynamicQueueEtaEquation = true;
        public boolean healthCheck = true;
        public long playerListsRefreshIntervalMins = 1440L; // one day as default
        public final Spectator spectator = new Spectator();
        public final LoginRateLimiter loginRateLimiter = new LoginRateLimiter();
        public boolean connectionTestOnStart = true;
        public final PacketRateLimiter packetRateLimiter = new PacketRateLimiter();
        public boolean upnp = false;
        public boolean injectTablistFooter = true;
        public boolean welcomeMessages = true;

        public static final class PacketRateLimiter {
            public boolean enabled = true;
            public double intervalSeconds = 7.0;
            public int maxPacketsPerInterval = 500;
            public boolean logRate = false;
        }

        public static final class LoginRateLimiter {
            public boolean enabled = true;
            public int rateLimitSeconds = 2;
        }

        public static final class Spectator {
            public boolean allowSpectator = true;
            public String spectatorEntity = "cat";
            public boolean spectatorPublicChatEnabled = true;
            public boolean fullCommandsEnabled = false;
            public boolean fullCommandsAcceptSlashCommands = true;
            public boolean fullCommandsRequireRegularWhitelist = true;
            public boolean playerCamOnJoin = false;
            public boolean whitelistEnabled = true;
            public ArrayList<PlayerEntry> whitelist = new ArrayList<>();
        }

        public static final class Bind {
            public String address = "0.0.0.0";
            public int port = 25565;
        }

        public static final class Extra {
            public final ServerTimeout timeout = new ServerTimeout();
            public final Whitelist whitelist = new Whitelist();
            public final ChatHistory chatHistory = new ChatHistory();
            public final ServerSwitcher serverSwitcher = new ServerSwitcher();

            public static class ServerSwitcher {
                public ArrayList<ServerSwitcherServer> servers = new ArrayList<>();
                public record ServerSwitcherServer(String name, String address, int port) { }
            }

            public static class ChatHistory {
                public boolean enable = false;
                public int seconds = 30;
                public int maxCount = 10;
                public boolean spectators = true;
            }

            public static final class Whitelist {
                public boolean enable = true;
                public final ArrayList<PlayerEntry> whitelist = new ArrayList<>();
                public String kickmsg = "no whitelist?";
                // Automatically adds the proxy client account to the whitelist if not present
                // does not remove any entries
                public boolean autoAddClient = true;
                // only checked when whitelist is disabled
                public final ArrayList<PlayerEntry> blacklist = new ArrayList<>();
            }

            public static final class ServerTimeout {
                public boolean enable = true;
                public int seconds = 30;
            }
        }

        public static final class Ping {
            public boolean enabled = true;
            public boolean onlinePlayers = false;
            public boolean onlinePlayerCount = true;
            public boolean favicon = true;
            public int maxPlayers = Integer.MAX_VALUE;
            public boolean lanBroadcast = true;
            public boolean responseCaching = true;
            // could probably be increased 2-3x without issue
            public int responseCacheSeconds = 10;
            public boolean logPings = true;
        }

        public static final class ServerViaVersion {
            public boolean enabled = true;
            public boolean autoRemoveFromPipeline = true;
        }

        public String getProxyAddress() {
            // if the proxy IP is not a DNS name, also return the port appended
            if (!this.proxyIP.contains(":") // port already appended
                && this.proxyIP.contains("[0-9]+\\.[0-9]+\\.[0-9]+\\.[0-9]+")) // IP address
                return this.proxyIP + ":" + this.bind.port;
             else
                return this.proxyIP;
        }

        public String getProxyAddressForTransfer() {
            // get only the IP/address part. no port
            if (this.proxyIP.contains(":")) {
                return this.proxyIP.split(":")[0];
            } else {
                return this.proxyIP;
            }
        }

        public int getProxyPortForTransfer() {
            // get only the port part. no IP/address
            if (this.proxyIP.contains(":")) {
                return Integer.parseInt(this.proxyIP.split(":")[1]);
            } else {
                return this.bind.port;
            }
        }
    }

    public static class Plugins {
        public boolean enabled = true;
    }

    public static final class InteractiveTerminal {
        public boolean enable = true;
        public boolean logToDiscord = true;
        public boolean allowDumbTerminal = true;
        public boolean alwaysOnCompletions = true;
    }

    public static final class InGameCommands {
        public boolean enable = true;
        public boolean slashCommands = true;
        public boolean slashCommandsReplacesServerCommands = false;
        public String prefix = "!";
        public boolean logToDiscord = true;
        public boolean allowWhitelistedToUseAccountOwnerCommands = false;
    }

    public static final class Discord {
        public boolean enable = false;
        public String token = "";
        public String channelId = "";
        public String accountOwnerRoleId = "";
        public String notificationMentionRoleId = "";
        public String prefix = ".";
        public boolean ignoreOtherBots = true;
        public boolean reportCoords = true;
        public boolean mentionRoleOnConnect = false;
        public boolean mentionRoleOnPlayerOnline = false;
        public boolean mentionRoleOnDisconnect = false;
        public boolean mentionRoleOnStartQueue = false;
        public boolean mentionRoleOnDeath = false;
        public boolean mentionRoleOnServerRestart = false;
        public boolean mentionRoleOnLoginFailed = false;
        public boolean clientConnectionMessages = true;
        public boolean mentionOnClientConnected = false;
        public boolean mcVersionMismatchWarning = true;
        public boolean mentionOnSpectatorConnected = false;
        public boolean mentionOnClientDisconnected = false;
        public boolean mentionOnNonWhitelistedClientConnected = false;
        public boolean mentionOnSpectatorDisconnected = false;
        public boolean mentionRoleOnPrioUpdate = true;
        public boolean mentionRoleOnPrioBanUpdate = true;
        public boolean mentionRoleOnDeviceCodeAuth = true;
        public boolean manageProfileImage = true;
        public boolean manageNickname = true;
        public boolean manageDescription = true;
        public boolean showNonWhitelistLoginIP = true;
        public boolean isUpdating = false; // internal use for update command state persistence
        public final ChatRelay chatRelay = new ChatRelay();

        public static class ChatRelay {
            public boolean enable = false;
            public boolean ignoreQueue = true;
            public boolean mentionRoleOnWhisper = true;
            public boolean mentionRoleOnNameMention = true;
            public boolean mentionWhileConnected = false;
            public boolean connectionMessages = false;
            public boolean publicChats = true;
            public boolean whispers = true;
            public boolean serverMessages = true;
            public boolean deathMessages = true;
            public boolean sendMessages = true;
            public String channelId = "";
        }
    }

    public static final class Database {
        public boolean enabled = false;
        public String host = "";
        public int port = 5432;
        public String username = "";
        public String password = "";
        public boolean queueWaitEnabled = true;
        public boolean connectionsEnabled = true;
        public boolean chatsEnabled = true;
        public boolean deathsEnabled = true;
        public boolean unknownDeathDiscordMsg = true;
        public boolean queueLengthEnabled = true;
        public boolean restartsEnabled = true;
        public boolean playerCountEnabled = true;
        public boolean tablistEnabled = true;
        public boolean playtimeEnabled = true;
        public boolean timeEnabled = true;
        public final Lock lock = new Lock();

        public static final class Lock {
            // use "rediss://" for SSL connection
            public String redisAddress = "redis://localhost:7181";
            public String redisUsername = "";
            public String redisPassword = "";
        }
    }

    public static final class AutoUpdater {
        public int autoUpdateCheckIntervalSeconds = 300;
        // internal config, don't set this manually
        public boolean shouldReconnectAfterAutoUpdate = false;
    }
}
