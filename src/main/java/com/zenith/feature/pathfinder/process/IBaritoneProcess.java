package com.zenith.feature.pathfinder.process;

import com.zenith.feature.pathfinder.PathingCommand;
import com.zenith.feature.pathfinder.PathingCommandType;
import com.zenith.feature.pathfinder.behavior.PathingBehavior;
import com.zenith.feature.pathfinder.goals.Goal;
import com.zenith.feature.pathfinder.util.PathEvent;

/**
 * A process that can control the PathingBehavior.
 * <p>
 * Differences between a baritone process and a behavior:
 * <ul>
 * <li>Only one baritone process can be active at a time</li>
 * <li>PathingBehavior can only be controlled by a process</li>
 * </ul>
 * <p>
 * That's it actually
 *
 * <AUTHOR>
 */
public interface IBaritoneProcess {

    /**
     * Default priority. Most normal processes should have this value.
     * <p>
     * Some examples of processes that should have different values might include some kind of automated mob avoidance
     * that would be temporary and would forcefully take control. Same for something that pauses pathing for auto eat, etc.
     * <p>
     * The value is -1 beacuse that's what Impact 4.5's beta auto walk returns and I want to tie with it.
     */
    double DEFAULT_PRIORITY = -1;

    /**
     * Would this process like to be in control?
     *
     * @return Whether or not this process would like to be in contorl.
     */
    boolean isActive();

    /**
     * Called when this process is in control of pathing; Returns what Baritone should do.
     *
     * @param calcFailed     {@code true} if this specific process was in control last tick,
     *                       and there was a {@link PathEvent#CALC_FAILED} event last tick
     * @param isSafeToCancel {@code true} if a {@link PathingCommandType#REQUEST_PAUSE} would happen this tick, and
     *                       {@link PathingBehavior} wouldn't actually tick. {@code false} if the PathExecutor reported
     *                       pausing would be unsafe at the end of the last tick. Effectively "could request cancel or
     *                       pause and have it happen right away"
     * @param currentGoal
     * @param prevCommand
     * @return What the {@link PathingBehavior} should do
     */
    PathingCommand onTick(boolean calcFailed, boolean isSafeToCancel, final Goal currentGoal, final PathingCommand prevCommand);

    /**
     * Returns whether or not this process should be treated as "temporary".
     * <p>
     * If a process is temporary, it doesn't call {@link #onLostControl} on the processes that aren't execute because of it.
     * <p>
     * For example, {@code CombatPauserProcess} and {@code PauseForAutoEatProcess} should return {@code true} always,
     * and should return {@link #isActive} {@code true} only if there's something in range this tick, or if the player would like
     * to start eating this tick. {@code PauseForAutoEatProcess} should only actually right click once onTick is called with
     * {@code isSafeToCancel} true though.
     *
     * @return Whether or not if this control is temporary
     */
    boolean isTemporary();

    /**
     * Called if {@link #isActive} returned {@code true}, but another non-temporary
     * process has control. Effectively the same as cancel. You want control but you
     * don't get it.
     */
    void onLostControl();

    void stop();

    /**
     * Used to determine which Process gains control if multiple are reporting {@link #isActive()}. The one
     * that returns the highest value will be given control.
     *
     * @return A double representing the priority
     */
    default double priority() {
        return DEFAULT_PRIORITY;
    }

    /**
     * Returns a user-friendly name for this process. Suitable for a HUD.
     *
     * @return A display name that's suitable for a HUD
     */
    default String displayName() {
        if (!isActive()) {
            // i love it when impcat's scuffed HUD calls displayName for inactive processes for 1 tick too long
            // causing NPEs when the displayname relies on fields that become null when inactive
            return "INACTIVE";
        }
        return displayName0();
    }

    String displayName0();
}
