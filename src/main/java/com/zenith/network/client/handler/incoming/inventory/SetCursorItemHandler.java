package com.zenith.network.client.handler.incoming.inventory;

import com.zenith.network.client.ClientSession;
import com.zenith.network.codec.ClientEventLoopPacketHandler;
import org.geysermc.mcprotocollib.protocol.packet.ingame.clientbound.inventory.ClientboundSetCursorItemPacket;

import static com.zenith.Globals.CACHE;

public class Set<PERSON>ursor<PERSON><PERSON><PERSON><PERSON><PERSON> implements ClientEventLoopPacketHandler<ClientboundSetCursorItemPacket, ClientSession> {
    @Override
    public boolean applyAsync(final ClientboundSetCursorItemPacket packet, final ClientSession session) {
        CACHE.getPlayerCache().getInventoryCache().setMouseStack(packet.getContents());
        return true;
    }
}
