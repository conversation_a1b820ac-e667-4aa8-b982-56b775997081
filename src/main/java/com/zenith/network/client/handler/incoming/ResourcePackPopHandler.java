package com.zenith.network.client.handler.incoming;

import com.zenith.network.client.ClientSession;
import com.zenith.network.codec.PacketHandler;
import org.geysermc.mcprotocollib.protocol.packet.common.clientbound.ClientboundResourcePackPopPacket;

import static com.zenith.Globals.CACHE;

public class ResourcePack<PERSON>opHandler implements PacketHandler<ClientboundResourcePackPopPacket, ClientSession> {
    public static final ResourcePackPopHandler INSTANCE = new ResourcePackPopHandler();
    @Override
    public ClientboundResourcePackPopPacket apply(final ClientboundResourcePackPopPacket packet, final ClientSession session) {
        if (packet.getId() == null) {
            CACHE.getConfigurationCache().getResourcePacks().clear();
        } else {
            CACHE.getConfigurationCache().getResourcePacks().remove(packet.getId());
        }
        return packet;
    }
}
