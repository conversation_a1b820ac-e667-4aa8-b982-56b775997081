package com.zenith.network.client.handler.incoming.entity;

import com.zenith.cache.data.entity.Entity;
import com.zenith.network.client.ClientSession;
import com.zenith.network.codec.ClientEventLoopPacketHandler;
import org.geysermc.mcprotocollib.protocol.packet.ingame.clientbound.entity.ClientboundUpdateAttributesPacket;
import org.jspecify.annotations.NonNull;

import static com.zenith.Globals.CACHE;

public class UpdateAttributes<PERSON>andler implements ClientEventLoopPacketHandler<ClientboundUpdateAttributesPacket, ClientSession> {
    @Override
    public boolean applyAsync(@NonNull ClientboundUpdateAttributesPacket packet, @NonNull ClientSession session) {
        Entity entity = CACHE.getEntityCache().get(packet.getEntityId());
        if (entity != null) {
            entity.updateAttributes(packet.getAttributes());
        }
        return true;
    }
}
