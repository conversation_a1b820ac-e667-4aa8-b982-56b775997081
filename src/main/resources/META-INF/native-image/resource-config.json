{"resources": {"includes": [{"pattern": "java.base:\\Qjdk/internal/icu/impl/data/icudt72b/nfkc.nrm\\E"}, {"pattern": "java.base:\\Qjdk/internal/icu/impl/data/icudt72b/uprops.icu\\E"}, {"pattern": "java.base:\\Qsun/net/idn/uidna.spp\\E"}]}, "bundles": [{"name": "org.postgresql.translation.messages"}], "globs": [{"glob": "META-INF/MANIFEST.MF"}, {"glob": "META-INF/maven/org.jline/jline-native/pom.properties"}, {"glob": "META-INF/native/libnetty_transport_native_epoll_x86_64.so"}, {"glob": "META-INF/services/ch.qos.logback.classic.spi.Configurator"}, {"glob": "META-INF/services/com.viaversion.viaversion.libs.kyori.adventure.text.serializer.gson.GsonComponentSerializer$Provider"}, {"glob": "META-INF/services/com.viaversion.viaversion.libs.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer$Provider"}, {"glob": "META-INF/services/java.lang.System$LoggerFinder"}, {"glob": "META-INF/services/java.net.spi.InetAddressResolverProvider"}, {"glob": "META-INF/services/java.net.spi.URLStreamHandlerProvider"}, {"glob": "META-INF/services/java.nio.channels.spi.SelectorProvider"}, {"glob": "META-INF/services/java.nio.charset.spi.CharsetProvider"}, {"glob": "META-INF/services/java.rmi.server.RMIClassLoaderSpi"}, {"glob": "META-INF/services/java.sql.Driver"}, {"glob": "META-INF/services/java.time.zone.ZoneRulesProvider"}, {"glob": "META-INF/services/java.util.spi.ResourceBundleControlProvider"}, {"glob": "META-INF/services/javax.xml.parsers.DocumentBuilderFactory"}, {"glob": "META-INF/services/javax.xml.parsers.SAXParserFactory"}, {"glob": "META-INF/services/net.kyori.adventure.internal.properties.AdventureProperties$DefaultOverrideProvider"}, {"glob": "META-INF/services/net.kyori.adventure.text.event.DataComponentValueConverterRegistry$Provider"}, {"glob": "META-INF/services/net.kyori.adventure.text.logger.slf4j.ComponentLoggerProvider"}, {"glob": "META-INF/services/net.kyori.adventure.text.minimessage.MiniMessage$Provider"}, {"glob": "META-INF/services/net.kyori.adventure.text.serializer.ansi.ANSIComponentSerializer$Provider"}, {"glob": "META-INF/services/net.kyori.adventure.text.serializer.gson.GsonComponentSerializer$Provider"}, {"glob": "META-INF/services/net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer$Provider"}, {"glob": "META-INF/services/org.apache.commons.logging.LogFactory"}, {"glob": "META-INF/services/org.cloudburstmc.math.vector.VectorProvider"}, {"glob": "META-INF/services/org.slf4j.spi.SLF4JServiceProvider"}, {"glob": "META-INF/services/org/jline/terminal/provider/exec"}, {"glob": "META-INF/services/org/jline/terminal/provider/ffm"}, {"glob": "META-INF/services/org/jline/terminal/provider/jansi"}, {"glob": "META-INF/services/org/jline/terminal/provider/jna"}, {"glob": "META-INF/services/org/jline/terminal/provider/jni"}, {"glob": "assets/**"}, {"glob": "assets/viabackwards/config.yml"}, {"glob": "assets/viabackwards/data/chat-types-1.19.1.nbt"}, {"glob": "assets/viabackwards/data/item-mappings-1.10.json"}, {"glob": "assets/viabackwards/data/item-mappings-1.11.1.json"}, {"glob": "assets/viabackwards/data/item-mappings-1.11.json"}, {"glob": "assets/viabackwards/data/item-mappings-1.12.json"}, {"glob": "assets/viabackwards/data/mappings-1.10to1.9.4.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.11to1.10.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.12to1.11.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.13.2to1.13.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.13to1.12.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.14to1.13.2.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.15to1.14.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.16.2to1.16.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.16to1.15.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.17to1.16.2.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.18to1.17.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.19.3to1.19.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.19.4to1.19.3.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.19to1.18.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.20.2to1.20.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.20.3to1.20.2.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.20.5to1.20.3.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.20to1.19.4.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.21.2to1.21.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.21.4to1.21.2.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.21.5to1.21.4.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.21.6to1.21.5.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.21.7to1.21.6.nbt"}, {"glob": "assets/viabackwards/data/mappings-1.21to1.20.5.nbt"}, {"glob": "assets/viabackwards/data/sounds-1.19.json"}, {"glob": "assets/viabackwards/data/translation-mappings.json"}, {"glob": "assets/viabackwards/data/trim_pattern-1.19.4.nbt"}, {"glob": "assets/viaversion/config.yml"}, {"glob": "assets/viaversion/data/blockConnections.nbt"}, {"glob": "assets/viaversion/data/blockIds1.12to1.13.json"}, {"glob": "assets/viaversion/data/blockNumberToString1.12.json"}, {"glob": "assets/viaversion/data/blockstates-1.13.nbt"}, {"glob": "assets/viaversion/data/channelmappings-1.13.json"}, {"glob": "assets/viaversion/data/chat-registry-1.19.1.nbt"}, {"glob": "assets/viaversion/data/chat-registry-1.19.nbt"}, {"glob": "assets/viaversion/data/chat-types-1.19.nbt"}, {"glob": "assets/viaversion/data/damage-types-1.19.4.nbt"}, {"glob": "assets/viaversion/data/damage-types-1.20.3.nbt"}, {"glob": "assets/viaversion/data/dimension-registry-1.16.2.nbt"}, {"glob": "assets/viaversion/data/en_US.properties"}, {"glob": "assets/viaversion/data/enchantments-1.21.nbt"}, {"glob": "assets/viaversion/data/extra-identifiers-1.20.3.nbt"}, {"glob": "assets/viaversion/data/heightmap-1.14.nbt"}, {"glob": "assets/viaversion/data/identifier-table.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.10.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.11.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.12.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.13.2.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.13.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.14.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.15.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.16.2.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.16.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.17.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.18.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.19.3.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.19.4.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.19.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.20.2.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.20.3.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.20.5.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.20.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.21.2.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.21.4.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.21.5.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.21.6.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.21.7.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.21.nbt"}, {"glob": "assets/viaversion/data/identifiers-1.9.4.nbt"}, {"glob": "assets/viaversion/data/itemrecipes1_12_2to1_13.json"}, {"glob": "assets/viaversion/data/jukebox-songs-1.21.nbt"}, {"glob": "assets/viaversion/data/mapping-lang-1.12-1.13.json"}, {"glob": "assets/viaversion/data/mappings-1.12to1.13.nbt"}, {"glob": "assets/viaversion/data/mappings-1.13.2to1.14.nbt"}, {"glob": "assets/viaversion/data/mappings-1.13to1.13.2.nbt"}, {"glob": "assets/viaversion/data/mappings-1.14to1.15.nbt"}, {"glob": "assets/viaversion/data/mappings-1.15to1.16.nbt"}, {"glob": "assets/viaversion/data/mappings-1.16.2to1.17.nbt"}, {"glob": "assets/viaversion/data/mappings-1.16to1.16.2.nbt"}, {"glob": "assets/viaversion/data/mappings-1.17to1.18.nbt"}, {"glob": "assets/viaversion/data/mappings-1.18to1.19.nbt"}, {"glob": "assets/viaversion/data/mappings-1.19.3to1.19.4.nbt"}, {"glob": "assets/viaversion/data/mappings-1.19.4to1.20.nbt"}, {"glob": "assets/viaversion/data/mappings-1.19to1.19.3.nbt"}, {"glob": "assets/viaversion/data/mappings-1.20.2to1.20.3.nbt"}, {"glob": "assets/viaversion/data/mappings-1.20.3to1.20.5.nbt"}, {"glob": "assets/viaversion/data/mappings-1.20.5to1.21.nbt"}, {"glob": "assets/viaversion/data/mappings-1.20to1.20.2.nbt"}, {"glob": "assets/viaversion/data/mappings-1.21.2to1.21.4.nbt"}, {"glob": "assets/viaversion/data/mappings-1.21.4to1.21.5.nbt"}, {"glob": "assets/viaversion/data/mappings-1.21.5to1.21.6.nbt"}, {"glob": "assets/viaversion/data/mappings-1.21.6to1.21.7.nbt"}, {"glob": "assets/viaversion/data/mappings-1.21to1.21.2.nbt"}, {"glob": "commons-logging.properties"}, {"glob": "death_message_mobs.schema"}, {"glob": "death_messages.schema"}, {"glob": "discord4j/common/git.properties"}, {"glob": "jndi.properties"}, {"glob": "linux_x86_64/velocity-cipher-ossl11x.so"}, {"glob": "linux_x86_64/velocity-cipher-ossl30x-musl.so"}, {"glob": "linux_x86_64/velocity-cipher-ossl30x.so"}, {"glob": "linux_x86_64/velocity-compress-musl.so"}, {"glob": "linux_x86_64/velocity-compress.so"}, {"glob": "logback-test.scmo"}, {"glob": "logback-test.xml"}, {"glob": "logback.scmo"}, {"glob": "logback.xml"}, {"glob": "mcdata/**"}, {"glob": "mcdata/blockCollisionShapes.json"}, {"glob": "mcdata/blockInteractionShapes.json"}, {"glob": "mcdata/fluidStates.json"}, {"glob": "mcdata/language.json"}, {"glob": "mcdata/mapColorIdToColor.json"}, {"glob": "mcdata/pathfindable.json"}, {"glob": "mcdata/replaceable.json"}, {"glob": "mcdata/slabBlockStateIds.json"}, {"glob": "mozilla/public-suffix-list.txt"}, {"glob": "org/apache/http/client/version.properties"}, {"glob": "org/fusesource/jansi/internal/native/**"}, {"glob": "org/fusesource/jansi/jansi.properties"}, {"glob": "org/jline/nativ/Linux/x86_64/libjlinenative.so"}, {"glob": "org/jline/nativ/Windows/x86_64/jlinenative.dll"}, {"glob": "org/jline/utils/*.caps"}, {"glob": "org/jline/utils/capabilities.txt"}, {"glob": "org/jline/utils/dumb-color.caps"}, {"glob": "org/jline/utils/screen.caps"}, {"glob": "org/jline/utils/windows-vtp.caps"}, {"glob": "org/jline/utils/xterm-256color.caps"}, {"glob": "org/postgresql/driverconfig.properties"}, {"glob": "servericon.png"}, {"glob": "yggdrasil_session_pubkey.der"}, {"glob": "zenith_commit.txt"}, {"glob": "zenith_mc_version.txt"}, {"glob": "zenith_release.txt"}, {"module": "java.base", "glob": "jdk/internal/icu/impl/data/icudt74b/nfkc.nrm"}, {"module": "java.base", "glob": "jdk/internal/icu/impl/data/icudt74b/uprops.icu"}, {"module": "java.base", "glob": "jdk/internal/icu/impl/data/icudt76b/nfc.nrm"}, {"module": "java.base", "glob": "jdk/internal/icu/impl/data/icudt76b/nfkc.nrm"}, {"module": "java.base", "glob": "jdk/internal/icu/impl/data/icudt76b/uprops.icu"}, {"module": "java.base", "glob": "sun/launcher/resources/launcher_en.properties"}, {"module": "java.base", "glob": "sun/launcher/resources/launcher_en_US.properties"}, {"module": "java.base", "glob": "sun/net/idn/uidna.spp"}, {"module": "java.xml", "glob": "jdk/xml/internal/jdkcatalog/JDKCatalog.xml"}, {"module": "jdk.jfr", "glob": "jdk/jfr/internal/query/view.ini"}, {"module": "jdk.jfr", "glob": "jdk/jfr/internal/types/metadata.bin"}]}