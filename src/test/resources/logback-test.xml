<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration>

<configuration scan="false">
    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>
    <import class="ch.qos.logback.classic.encoder.PatternLayoutEncoder"/>
    <import class="ch.qos.logback.classic.filter.ThresholdFilter"/>
    <import class="ch.qos.logback.classic.filter.LevelFilter"/>
    <import class="ch.qos.logback.core.ConsoleAppender"/>
    <import class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy"/>

    <statusListener class="com.zenith.terminal.logback.PrintOnlyErrorLogbackStatusListener"/>
    <!-- Prevent logback compression failing during JVM stop https://logback.qos.ch/manual/configuration.html#shutdownHook -->
    <shutdownHook/>

    <appender name="TERMINAL" class="com.zenith.terminal.logback.TerminalConsoleAppender">
        <encoder class="com.zenith.terminal.logback.MCTextFormatANSIEncoder">
            <pattern>[%d{yyyy/MM/dd HH:mm:ss}] [%logger{36}] [%level] %minecraftText%n</pattern>
        </encoder>
        <filter class="com.zenith.terminal.logback.LogSourceFilter"/>
        <filter class="ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>
    <appender name="ASYNC_TERMINAL" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="TERMINAL"/>
        <discardingThreshold>0</discardingThreshold>
    </appender>

    <root>
        <!--        allow logs from other libs to also go to stdout-->
        <appender-ref ref="ASYNC_TERMINAL"/>
    </root>
</configuration>
